#!/usr/bin/env python3
"""
Тестовый файл для проверки новой функциональности статистики тестов месяца
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from common.tests_statistics.handlers import (
    get_student_level,
    calculate_kpi_achievement,
    calculate_level_statistics
)

def test_student_levels():
    """Тест функции определения уровня студента"""
    print("=== Тест определения уровней студентов ===")
    
    test_cases = [
        (0, "Слабый"),
        (25, "Слабый"),
        (40, "Слабый"),
        (41, "Средний"),
        (55, "Средний"),
        (70, "Средний"),
        (71, "Сильный"),
        (85, "Сильный"),
        (100, "Сильный")
    ]
    
    for percentage, expected_level in test_cases:
        actual_level = get_student_level(percentage)
        status = "✅" if actual_level == expected_level else "❌"
        print(f"{status} {percentage}% -> {actual_level} (ожидалось: {expected_level})")

def test_kpi_achievement():
    """Тест функции расчета KPI"""
    print("\n=== Тест расчета KPI ===")
    
    test_cases = [
        # Слабые студенты (рост ≥50%)
        (20, 30, "Слабый", True),   # 20 + 50% = 30
        (20, 35, "Слабый", True),   # больше требуемого
        (20, 25, "Слабый", False),  # меньше требуемого
        (0, 50, "Слабый", True),    # из 0% в 50%
        (0, 30, "Слабый", False),   # из 0% в 30% (мало)
        
        # Средние студенты (рост ≥30%)
        (50, 65, "Средний", True),  # 50 + 30% = 65
        (50, 70, "Средний", True),  # больше требуемого
        (50, 60, "Средний", False), # меньше требуемого
        
        # Сильные студенты (сохранить или улучшить)
        (80, 80, "Сильный", True),  # сохранил
        (80, 85, "Сильный", True),  # улучшил
        (80, 75, "Сильный", False), # ухудшил
    ]
    
    for entry_pct, control_pct, level, expected_result in test_cases:
        actual_result = calculate_kpi_achievement(entry_pct, control_pct, level)
        status = "✅" if actual_result == expected_result else "❌"
        print(f"{status} {level}: {entry_pct}% -> {control_pct}% = {actual_result} (ожидалось: {expected_result})")

def test_level_statistics():
    """Тест функции подсчета статистики по уровням"""
    print("\n=== Тест статистики по уровням ===")
    
    # Создаем mock объекты результатов тестов
    class MockTestResult:
        def __init__(self, score_percentage):
            self.score_percentage = score_percentage
    
    test_results = [
        MockTestResult(25),   # Слабый
        MockTestResult(35),   # Слабый
        MockTestResult(55),   # Средний
        MockTestResult(65),   # Средний
        MockTestResult(75),   # Сильный
        MockTestResult(85),   # Сильный
        MockTestResult(95),   # Сильный
    ]
    
    stats = calculate_level_statistics(test_results)
    
    print(f"Слабых: {stats['weak_count']} (ожидалось: 2)")
    print(f"Средних: {stats['medium_count']} (ожидалось: 2)")
    print(f"Сильных: {stats['strong_count']} (ожидалось: 3)")
    print(f"Всего: {stats['total_count']} (ожидалось: 7)")
    
    # Проверка
    expected = {"weak_count": 2, "medium_count": 2, "strong_count": 3, "total_count": 7}
    all_correct = all(stats[key] == expected[key] for key in expected)
    status = "✅" if all_correct else "❌"
    print(f"{status} Статистика по уровням")

if __name__ == "__main__":
    test_student_levels()
    test_kpi_achievement()
    test_level_statistics()
    print("\n=== Тестирование завершено ===")
