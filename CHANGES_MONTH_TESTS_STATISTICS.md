# Изменения в статистике тестов месяца

## Обзор изменений

Реализована новая система уровней студентов и KPI для входного и контрольного тестов месяца согласно требованиям.

## Новая функциональность

### 1. Система уровней студентов

**Уровни определяются по проценту правильных ответов:**
- **Слабый**: 0-40%
- **Средний**: 41-70%  
- **Сильный**: 71-100%

### 2. KPI система для контрольного теста

**Требования для выполнения KPI:**
- **Слабые студенты**: должны вырасти минимум на 50%
- **Средние студенты**: должны вырасти минимум на 30%
- **Сильные студенты**: должны сохранить свой уровень или повысить его

### 3. Изменения в отображении

#### Входной тест месяца:
```
✅ Прошли тест:
1. Мадияр Карибжанов (Слабый)
2. Иван Петров (Средний)
3. Анна Сидорова (Сильный)

❌ Не прошли тест:
Все студенты прошли тест

Слабых 0–40%: 1 (количество)
Средних 41–70%: 1
Сильных 71–100%: 1
```

#### Контрольный тест месяца:
```
✅ Прошли тест:
1. Мадияр Карибжанов (85%)
2. Иван Петров (75%)
3. Анна Сидорова (90%)

❌ Не прошли тест:
Все студенты прошли тест

📈 KPI выполнение: 66.7% (2/3)
Слабые (рост ≥50%): 100% (1/1)
Средние (рост ≥30%): 100% (1/1)
Сильные (сохранили/улучшили): 0% (0/1)
```

## Технические изменения

### Новые функции в `common/tests_statistics/handlers.py`:

1. **`get_student_level(score_percentage: float) -> str`**
   - Определяет уровень студента по проценту

2. **`calculate_kpi_achievement(entry_percentage: float, control_percentage: float, entry_level: str) -> bool`**
   - Проверяет выполнение KPI для контрольного теста

3. **`calculate_level_statistics(test_results: list) -> dict`**
   - Подсчитывает статистику по уровням

4. **`calculate_control_test_kpi_statistics(group_id: int, month_test_id: int) -> dict`**
   - Рассчитывает KPI статистику для контрольного теста

### Новые методы в репозиториях:

1. **`MonthEntryTestResultRepository.get_by_group_and_month_test(group_id: int, month_test_id: int)`**
   - Получает все результаты входного теста для группы и теста

2. **`MonthControlTestResultRepository.get_by_group_and_month_test(group_id: int, month_test_id: int)`**
   - Получает все результаты контрольного теста для группы и теста

### Обновленные функции:

1. **`show_month_entry_test_statistics()`**
   - Добавлено отображение уровней студентов
   - Добавлена статистика по количеству слабых/средних/сильных

2. **`show_month_control_test_statistics()`**
   - Добавлена KPI статистика
   - Добавлена детализация по уровням с процентом выполнения KPI

## Совместимость

- Все изменения обратно совместимы
- Существующая функциональность сохранена
- Работает для всех ролей: куратор, учитель, менеджер
- Фильтрация данных по ролям сохранена

## Тестирование

Все новые функции протестированы:
- ✅ Определение уровней студентов
- ✅ Расчет KPI для всех типов студентов
- ✅ Подсчет статистики по уровням
- ✅ Обработка граничных случаев (0%, 100%, равные значения)
