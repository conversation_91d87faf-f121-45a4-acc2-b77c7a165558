-- SQL запрос для расчета общего процента выполнения ДЗ всех учеников
-- Этот запрос рассчитывает процент как: (уникальные выполненные ДЗ всех студентов) / (общее количество доступных ДЗ для всех студентов) * 100

WITH student_homework_stats AS (
    -- Для каждого студента считаем количество уникальных выполненных ДЗ и общее количество доступных ДЗ
    SELECT 
        s.id as student_id,
        s.user_id,
        u.name as student_name,
        
        -- <PERSON><PERSON><PERSON><PERSON>чество уникальных выполненных ДЗ студентом (по предметам его групп)
        COALESCE(
            (SELECT COUNT(DISTINCT hr.homework_id)
             FROM homework_results hr
             JOIN homeworks h ON hr.homework_id = h.id
             JOIN student_groups sg ON sg.student_id = s.id
             JOIN groups g ON sg.group_id = g.id
             WHERE hr.student_id = s.id 
               AND h.subject_id = g.subject_id), 
            0
        ) as unique_completed,
        
        -- Общее количество доступных ДЗ для студента (по предметам его групп)
        COALESCE(
            (SELECT COUNT(DISTINCT h.id)
             FROM homeworks h
             JOIN student_groups sg ON sg.student_id = s.id
             JOIN groups g ON sg.group_id = g.id
             WHERE h.subject_id = g.subject_id), 
            0
        ) as total_available
        
    FROM students s
    JOIN users u ON s.user_id = u.id
    WHERE EXISTS (
        -- Только студенты, которые состоят в группах
        SELECT 1 FROM student_groups sg WHERE sg.student_id = s.id
    )
),

overall_stats AS (
    -- Агрегируем статистику по всем студентам
    SELECT 
        COUNT(*) as total_students,
        SUM(unique_completed) as total_unique_completed,
        SUM(total_available) as total_available_homeworks,
        
        -- Общий процент выполнения ДЗ
        CASE 
            WHEN SUM(total_available) > 0 
            THEN ROUND((SUM(unique_completed) * 100.0) / SUM(total_available), 1)
            ELSE 0 
        END as overall_completion_percentage
        
    FROM student_homework_stats
    WHERE total_available > 0  -- Исключаем студентов без доступных ДЗ
)

-- Основной результат
SELECT 
    total_students as "Всего активных студентов",
    total_unique_completed as "Всего уникальных выполненных ДЗ",
    total_available_homeworks as "Всего доступных ДЗ",
    overall_completion_percentage as "Общий % выполнения ДЗ"
FROM overall_stats

UNION ALL

-- Дополнительная детализация по студентам (топ 10 лучших и худших)
SELECT 
    'Топ студенты:' as "Всего активных студентов",
    '' as "Всего уникальных выполненных ДЗ", 
    '' as "Всего доступных ДЗ",
    '' as "Общий % выполнения ДЗ"

UNION ALL

SELECT 
    student_name as "Всего активных студентов",
    CAST(unique_completed as TEXT) as "Всего уникальных выполненных ДЗ",
    CAST(total_available as TEXT) as "Всего доступных ДЗ", 
    CAST(
        CASE 
            WHEN total_available > 0 
            THEN ROUND((unique_completed * 100.0) / total_available, 1)
            ELSE 0 
        END as TEXT
    ) || '%' as "Общий % выполнения ДЗ"
FROM student_homework_stats
WHERE total_available > 0
ORDER BY 
    CASE 
        WHEN total_available > 0 
        THEN (unique_completed * 100.0) / total_available
        ELSE 0 
    END DESC
LIMIT 10;
